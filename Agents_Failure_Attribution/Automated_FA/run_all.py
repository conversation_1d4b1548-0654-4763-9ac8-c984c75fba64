#!/usr/bin/env python3

import subprocess
import os

# Parameters
methods = ["all_at_once", "step_by_step", "binary_search"]
model = "gpt-4o"
azure_endpoint = "https://cloudgpt-openai.azure-api.net/"
api_version = "2025-04-01-preview"

# Data configurations
configs = [
    {"is_handcrafted": "True", "directory": "../Who&When/Hand-Crafted", "suffix": "handcrafted"},
    {"is_handcrafted": "False", "directory": "../Who&When/Algorithm-Generated", "suffix": "algorithm-generated"}
]

for config in configs:
    for method in methods:
        print(f"\n🔄 Running {method} on {config['suffix']}...")
        
        # 1. Inference
        cmd1 = [
            "python", "inference_cloudgpt.py",
            "--method", method,
            "--model", model,
            "--azure_endpoint", azure_endpoint,
            "--api_version", api_version,
            "--is_handcrafted", config["is_handcrafted"],
            "--directory_path", config["directory"]
        ]
        subprocess.run(cmd1)
        
        # 2. Evaluation
        eval_file = f"outputs/{method}_{model}_{config['suffix']}.txt"
        if os.path.exists(eval_file):
            cmd2 = [
                "python", "evaluate_v2.py",
                "--eval_file", eval_file,
                "--data_path", config["directory"]
            ]
            subprocess.run(cmd2)
        else:
            print(f"❌ File not found: {eval_file}")

print("\n✅ All done!")
