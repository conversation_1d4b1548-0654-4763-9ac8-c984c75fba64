#!/usr/bin/env python3
"""
Simple batch script to run inference and evaluation
"""

import subprocess
import os

def run_cmd(cmd):
    """Run command and print status"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    if result.returncode == 0:
        print("✅ Success\n")
    else:
        print("❌ Failed\n")
    return result.returncode == 0

def main():
    methods = ["all_at_once", "step_by_step", "binary_search"]
    model = "gpt-4o"
    
    # Configurations: (is_handcrafted, directory, data_path, suffix)
    configs = [
        (True, "../Who&When/Hand-Crafted", "../Who&When/Hand-Crafted", "handcrafted"),
        (False, "../Who&When/Algorithm-Generated", "../Who&When/Algorithm-Generated", "algorithm-generated")
    ]
    
    total = len(methods) * len(configs)
    success = 0
    
    print(f"Starting {total} inference + evaluation runs...\n")
    
    for is_handcrafted, directory, data_path, suffix in configs:
        for method in methods:
            print(f"🔄 {method} on {suffix}")
            
            # 1. Inference
            inference_cmd = [
                "python", "inference_cloudgpt.py",
                "--method", method,
                "--model", model,
                "--azure_endpoint", "https://cloudgpt-openai.azure-api.net/",
                "--api_version", "2025-04-01-preview",
                "--is_handcrafted", str(is_handcrafted),
                "--directory_path", directory
            ]
            
            if not run_cmd(inference_cmd):
                continue
            
            # 2. Evaluation
            eval_file = f"outputs/{method}_{model}_{suffix}.txt"
            if not os.path.exists(eval_file):
                print(f"❌ Output file not found: {eval_file}\n")
                continue
                
            evaluate_cmd = [
                "python", "evaluate_v2.py",
                "--eval_file", eval_file,
                "--data_path", data_path
            ]
            
            if run_cmd(evaluate_cmd):
                success += 1
    
    print(f"Completed: {success}/{total} successful runs")

if __name__ == "__main__":
    main()
