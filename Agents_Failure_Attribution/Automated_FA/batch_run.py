#!/usr/bin/env python3
"""
Batch script to run inference and evaluation with different parameter combinations
"""

import subprocess
import os
import sys
from datetime import datetime

def run_command(cmd, description):
    """Run a command and handle errors"""
    print(f"\n{'='*60}")
    print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False, text=True)
        print(f"✅ SUCCESS: {description}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ FAILED: {description}")
        print(f"Error: {e}")
        return False

def main():
    # Configuration
    methods = ["all_at_once", "step_by_step", "binary_search"]
    model = "gpt-4o"
    azure_endpoint = "https://cloudgpt-openai.azure-api.net/"
    api_version = "2025-04-01-preview"
    
    # Parameter combinations
    configs = [
        {"is_handcrafted": True, "directory": "../Who&When/Hand-Crafted", "data_path": "../Who&When/Hand-Crafted"},
        {"is_handcrafted": False, "directory": "../Who&When/Algorithm-Generated", "data_path": "../Who&When/Algorithm-Generated"}
    ]
    
    total_runs = len(methods) * len(configs)
    current_run = 0
    successful_runs = 0
    failed_runs = []
    
    print(f"🚀 Starting batch inference and evaluation")
    print(f"Total combinations to run: {total_runs}")
    print(f"Methods: {methods}")
    print(f"Configurations: {len(configs)} (handcrafted + algorithm-generated)")
    print(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    for config in configs:
        is_handcrafted = config["is_handcrafted"]
        directory_path = config["directory"]
        data_path = config["data_path"]
        handcrafted_str = "handcrafted" if is_handcrafted else "algorithm-generated"
        
        print(f"\n🔄 Processing {handcrafted_str} data...")
        
        for method in methods:
            current_run += 1
            
            print(f"\n📊 Run {current_run}/{total_runs}: {method} on {handcrafted_str}")
            
            # Generate expected output filename
            handcrafted_suffix = "handcrafted" if is_handcrafted else "algorithm-generated"
            expected_output = f"outputs/{method}_{model}_{handcrafted_suffix}.txt"
            
            # 1. Run inference
            inference_cmd = [
                "python", "inference_cloudgpt.py",
                "--method", method,
                "--model", model,
                "--azure_endpoint", azure_endpoint,
                "--api_version", api_version,
                "--is_handcrafted", str(is_handcrafted),
                "--directory_path", directory_path
            ]
            
            inference_success = run_command(
                inference_cmd, 
                f"Inference: {method} on {handcrafted_str}"
            )
            
            if not inference_success:
                failed_runs.append(f"Inference: {method} on {handcrafted_str}")
                continue
            
            # 2. Check if output file exists
            if not os.path.exists(expected_output):
                print(f"❌ Expected output file not found: {expected_output}")
                failed_runs.append(f"Missing output: {method} on {handcrafted_str}")
                continue
            
            # 3. Run evaluation
            evaluate_cmd = [
                "python", "evaluate_v2.py",
                "--eval_file", expected_output,
                "--data_path", data_path
            ]
            
            evaluate_success = run_command(
                evaluate_cmd,
                f"Evaluation: {method} on {handcrafted_str}"
            )
            
            if evaluate_success:
                successful_runs += 1
                print(f"✅ COMPLETED: {method} on {handcrafted_str}")
            else:
                failed_runs.append(f"Evaluation: {method} on {handcrafted_str}")
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🏁 BATCH RUN COMPLETED")
    print(f"{'='*60}")
    print(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Total runs attempted: {total_runs}")
    print(f"Successful runs: {successful_runs}")
    print(f"Failed runs: {len(failed_runs)}")
    
    if failed_runs:
        print(f"\n❌ Failed runs:")
        for i, failure in enumerate(failed_runs, 1):
            print(f"  {i}. {failure}")
    
    if successful_runs == total_runs:
        print(f"\n🎉 All runs completed successfully!")
        return 0
    else:
        print(f"\n⚠️  Some runs failed. Check the logs above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
