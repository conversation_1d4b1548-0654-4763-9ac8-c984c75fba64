#!/bin/bash

# Batch script to run inference and evaluation
set -e  # Exit on error

MODEL="gpt-4o"
AZURE_ENDPOINT="https://cloudgpt-openai.azure-api.net/"
API_VERSION="2025-04-01-preview"

METHODS=("all_at_once" "step_by_step" "binary_search")

echo "🚀 Starting batch inference and evaluation..."
echo "Methods: ${METHODS[@]}"
echo "Model: $MODEL"
echo ""

SUCCESS_COUNT=0
TOTAL_COUNT=0

# Function to run inference and evaluation
run_experiment() {
    local method=$1
    local is_handcrafted=$2
    local directory=$3
    local data_path=$4
    local suffix=$5
    
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    
    echo "🔄 Running: $method on $suffix"
    echo "----------------------------------------"
    
    # Run inference
    echo "📥 Inference..."
    if python inference_cloudgpt.py \
        --method "$method" \
        --model "$MODEL" \
        --azure_endpoint "$AZURE_ENDPOINT" \
        --api_version "$API_VERSION" \
        --is_handcrafted "$is_handcrafted" \
        --directory_path "$directory"; then
        echo "✅ Inference completed"
    else
        echo "❌ Inference failed"
        return 1
    fi
    
    # Check output file exists
    EVAL_FILE="outputs/${method}_${MODEL}_${suffix}.txt"
    if [ ! -f "$EVAL_FILE" ]; then
        echo "❌ Output file not found: $EVAL_FILE"
        return 1
    fi
    
    # Run evaluation
    echo "📊 Evaluation..."
    if python evaluate_v2.py \
        --eval_file "$EVAL_FILE" \
        --data_path "$data_path"; then
        echo "✅ Evaluation completed"
        SUCCESS_COUNT=$((SUCCESS_COUNT + 1))
        echo "✅ SUCCESS: $method on $suffix"
    else
        echo "❌ Evaluation failed"
        return 1
    fi
    
    echo ""
}

# Run all combinations
for method in "${METHODS[@]}"; do
    # Handcrafted data
    run_experiment "$method" "True" "../Who&When/Hand-Crafted" "../Who&When/Hand-Crafted" "handcrafted" || true
    
    # Algorithm-generated data  
    run_experiment "$method" "False" "../Who&When/Algorithm-Generated" "../Who&When/Algorithm-Generated" "algorithm-generated" || true
done

echo "🏁 Batch run completed!"
echo "Success: $SUCCESS_COUNT/$TOTAL_COUNT"

if [ $SUCCESS_COUNT -eq $TOTAL_COUNT ]; then
    echo "🎉 All experiments completed successfully!"
    exit 0
else
    echo "⚠️  Some experiments failed."
    exit 1
fi
