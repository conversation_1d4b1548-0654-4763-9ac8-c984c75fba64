#!/bin/bash

methods=("all_at_once" "step_by_step" "binary_search")
model="gpt-4o"

for method in "${methods[@]}"; do
    echo "🔄 Running $method..."
    
    # Handcrafted
    python inference_cloudgpt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted True --directory_path "../Who&When/Hand-Crafted"
    python evaluate_v2.py --eval_file "outputs/${method}_${model}_handcrafted.txt" --data_path "../Who&When/Hand-Crafted"
    
    # Algorithm-generated  
    python inference_cloudgpt.py --method $method --model $model --azure_endpoint "https://cloudgpt-openai.azure-api.net/" --api_version "2025-04-01-preview" --is_handcrafted False --directory_path "../Who&When/Algorithm-Generated"
    python evaluate_v2.py --eval_file "outputs/${method}_${model}_algorithm-generated.txt" --data_path "../Who&When/Algorithm-Generated"
done

echo "✅ All done!"
